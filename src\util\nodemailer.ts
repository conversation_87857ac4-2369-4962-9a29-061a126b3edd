import nodemailer from 'nodemailer'
import smtpTransport from "nodemailer-smtp-transport";

export const SendEmailTemplet = async (email: string | undefined, subject: string, from?: any, body?: any, attachments?: any) => {
    try {
        const transporter = nodemailer.createTransport(
            smtpTransport({
                host: "stmp.gmail.com",
                port: 587,
                service: "gmail",
                requireTLS: true,
                auth: {
                    type: "OAuth2",
                    user: '<EMAIL>',
                    pass: 'jnbm yvkp scnp wagu',
                },
            })
        );
        const mailConfigurations: any = {
            from: process.env.SMPT_MAIL,
            to: email,
            subject: subject,
        };

        // Check if body contains HTML tags, if not treat as plain text
        if (body && body.includes('<')) {
            mailConfigurations.html = body;
        } else {
            mailConfigurations.text = body;
        }

        // Add attachments if provided
        if (attachments && attachments.length > 0) {
            mailConfigurations.attachments = attachments;
        }

        transporter.sendMail(mailConfigurations, function (error, info) {
            if (!info) {
                console.log(error);

            }

            return info?.messageId
        })
    } catch (error) {
        console.log(error)
    }
}
import { Response } from 'express';
import { CustomRequest } from '../util/Interface/expressInterface';
import { AppDataSource } from '../data-source';
import { LearnerPlan } from '../entity/LearnerPlan.entity';
import { Course } from '../entity/Course.entity';
import { UserCourse } from '../entity/UserCourse.entity';
import { SendNotification } from '../util/socket/notification';
import { NotificationType, SocketDomain } from '../util/constants';

class LearnerPlanController {

    public async createLearnerPlan(req: CustomRequest, res: Response) {
        try {
            const learnerPlanRepository = AppDataSource.getRepository(LearnerPlan);
            const courseRepository = AppDataSource.getRepository(Course);
            const userCourseRepository = AppDataSource.getRepository(UserCourse);

            const { assessor_id, learners, courses, title, description, location, startDate, Duration, type, Attended, repeatSession } = req.body;

            // Validate required fields
            if (!assessor_id || !learners || !Array.isArray(learners) || learners.length === 0) {
                return res.status(400).json({
                    message: "Assessor ID and learners are required",
                    status: false
                });
            }

            // Create learner plan
            const learnerPlan = learnerPlanRepository.create({
                assessor_id,
                learners: learners.map((id: number) => ({ learner_id: id })),
                courses: courses ? courses.map((id: number) => ({ course_id: id })) : [],
                title,
                description,
                location,
                startDate,
                Duration,
                type,
                Attended,
                repeatSession: repeatSession || false
            });

            const savedLearnerPlan = await learnerPlanRepository.save(learnerPlan);

            // Fetch learner plan with relations for response and notifications
            const learnerPlanWithRelations: any = await learnerPlanRepository.findOne({
                where: { learner_plan_id: savedLearnerPlan.learner_plan_id },
                relations: ['assessor_id', 'learners', 'learners.user_id', 'courses']
            });

            // Get filtered course list based on assessor and learners
            const filteredCourses = await this.getFilteredCourses(assessor_id, learners);

            console.log(`📧 Sending notifications to ${learnerPlanWithRelations?.learners.length} learners`);
            
            // Send notifications to learners (no email functionality as requested)
            learnerPlanWithRelations?.learners.forEach(async (learner: any) => {
                const learnerId = learner.user_id.user_id;
                const assessorName = learnerPlanWithRelations.assessor_id.first_name + " " + learnerPlanWithRelations.assessor_id.last_name;
                const planDate = new Date(learnerPlanWithRelations.startDate).toISOString().split('T')[0];
                
                console.log(`📤 Sending learner plan notification to learner ID: ${learnerId}`);
                
                const notificationData = {
                    data: {
                        title: "New Learner Plan",
                        message: `You have a new learner plan with ${assessorName} on ${planDate}`,
                        type: NotificationType.Notification
                    },
                    domain: SocketDomain.CourseAllocation
                };
                
                try {
                    await SendNotification(learnerId, notificationData);
                    console.log(`✅ Notification sent successfully to learner ${learnerId}`);
                } catch (error) {
                    console.error(`❌ Failed to send notification to learner ${learnerId}:`, error);
                }
            });

            return res.status(200).json({
                message: "Learner plan created successfully",
                status: true,
                data: {
                    ...savedLearnerPlan,
                    assessor_details: learnerPlanWithRelations.assessor_id,
                    learners: learnerPlanWithRelations.learners,
                    courses: learnerPlanWithRelations.courses,
                    filtered_course_list: filteredCourses
                }
            });

        } catch (error) {
            return res.status(500).json({
                message: "Internal Server Error",
                status: false,
                error: error.message
            });
        }
    }

    private async getFilteredCourses(assessorId: number, learnerIds: number[]) {
        try {
            const userCourseRepository = AppDataSource.getRepository(UserCourse);
            
            // Get courses where the assessor is involved (trainer, IQA, LIQA, EQA) and learners are assigned
            const filteredCourses = await userCourseRepository.createQueryBuilder('uc')
                .leftJoinAndSelect('uc.course', 'course')
                .leftJoinAndSelect('uc.learner_id', 'learner')
                .where('uc.learner_id IN (:...learnerIds)', { learnerIds })
                .andWhere(
                    '(uc.trainer_id = :assessorId OR uc.IQA_id = :assessorId OR uc.LIQA_id = :assessorId OR uc.EQA_id = :assessorId)',
                    { assessorId }
                )
                .select([
                    'uc.user_course_id',
                    'course.course_id',
                    'course.course_name',
                    'course.course_code',
                    'course.course_core_type',
                    'learner.learner_id',
                    'learner.first_name',
                    'learner.last_name'
                ])
                .getMany();

            return filteredCourses;
        } catch (error) {
            console.error('Error fetching filtered courses:', error);
            return [];
        }
    }

    public async updateLearnerPlan(req: CustomRequest, res: Response) {
        try {
            const learnerPlanRepository = AppDataSource.getRepository(LearnerPlan);

            const id = parseInt(req.params.id);
            const { title, description, location, startDate, Duration, type, Attended, repeatSession } = req.body;

            let learnerPlan = await learnerPlanRepository.findOne({ where: { learner_plan_id: id } });
            if (!learnerPlan) {
                return res.status(404).json({
                    message: "Learner plan not found",
                    status: false
                });
            }

            learnerPlan.title = title || learnerPlan.title;
            learnerPlan.description = description || learnerPlan.description;
            learnerPlan.location = location || learnerPlan.location;
            learnerPlan.startDate = startDate || learnerPlan.startDate;
            learnerPlan.Duration = Duration || learnerPlan.Duration;
            learnerPlan.type = type || learnerPlan.type;
            learnerPlan.Attended = Attended || learnerPlan.Attended;
            learnerPlan.repeatSession = repeatSession !== undefined ? repeatSession : learnerPlan.repeatSession;

            learnerPlan = await learnerPlanRepository.save(learnerPlan);

            return res.status(200).json({
                message: "Learner plan updated successfully",
                status: true,
                data: learnerPlan
            });

        } catch (error) {
            return res.status(500).json({
                message: "Internal Server Error",
                status: false,
                error: error.message
            });
        }
    }

    public async deleteLearnerPlan(req: CustomRequest, res: Response) {
        try {
            const id = parseInt(req.params.id);
            const learnerPlanRepository = AppDataSource.getRepository(LearnerPlan);

            const deleteResult = await learnerPlanRepository.delete(id);

            if (deleteResult.affected === 0) {
                return res.status(404).json({
                    message: 'Learner plan not found',
                    status: false,
                });
            }

            return res.status(200).json({
                message: 'Learner plan deleted successfully',
                status: true,
            });
        } catch (error) {
            return res.status(500).json({
                message: 'Internal Server Error',
                status: false,
                error: error.message,
            });
        }
    }

    public async getLearnerPlans(req: CustomRequest, res: Response) {
        try {
            const learnerPlanRepository = AppDataSource.getRepository(LearnerPlan);

            const { assessor_id, learners, type, Attended, sortBy } = req.query as any;

            const qb = learnerPlanRepository.createQueryBuilder('learnerPlan')
                .leftJoinAndSelect('learnerPlan.assessor_id', 'assessor')
                .leftJoinAndSelect('learnerPlan.learners', 'learner')
                .leftJoinAndSelect('learnerPlan.courses', 'course')
                .select([
                    'learnerPlan.learner_plan_id',
                    'learnerPlan.title',
                    'learnerPlan.location',
                    'learnerPlan.startDate',
                    'learnerPlan.Duration',
                    'learnerPlan.type',
                    'learnerPlan.Attended',
                    'learnerPlan.description',
                    'learnerPlan.repeatSession',
                    'assessor.user_id',
                    'assessor.user_name',
                    'assessor.email',
                    'learner.learner_id',
                    'learner.user_name',
                    'learner.email',
                    'course.course_id',
                    'course.course_name',
                    'course.course_code'
                ]);

            if (assessor_id) {
                qb.andWhere('assessor.user_id = :assessor_id', { assessor_id });
            }
            if (type) {
                qb.andWhere('learnerPlan.type = :type', { type });
            }
            if (Attended) {
                qb.andWhere('learnerPlan.Attended = :Attended', { Attended });
            }
            if (learners) {
                const learnerIds = learners.split(',');
                const learnerPlansWithLearner = await learnerPlanRepository.createQueryBuilder('learnerPlan')
                    .leftJoin('learnerPlan.learners', 'learner')
                    .where('learner.learner_id IN (:...learnerIds)', { learnerIds })
                    .select('learnerPlan.learner_plan_id')
                    .getMany();

                const learnerPlanIds = learnerPlansWithLearner.map(lp => lp.learner_plan_id);
                if (learnerPlanIds.length === 0) {
                    qb.andWhere('1 = 0');
                } else {
                    qb.andWhere('learnerPlan.learner_plan_id IN (:...learnerPlanIds)', { learnerPlanIds });
                }
            }

            // Add sorting for startDate
            const sortOrder = sortBy === 'asc' ? 'ASC' : 'DESC';
            
            qb.skip(req.pagination.skip)
                .take(Number(req.pagination.limit))
                .orderBy('learnerPlan.startDate', sortOrder);

            const [learnerPlans, count] = await qb.getManyAndCount();

            return res.status(200).json({
                message: "Learner plans fetched successfully",
                status: true,
                data: learnerPlans,
                ...(req.query.meta === "true" && {
                    meta_data: {
                        page: req.pagination.page,
                        items: count,
                        page_size: req.pagination.limit,
                        pages: Math.ceil(count / req.pagination.limit)
                    }
                })
            });

        } catch (error) {
            return res.status(500).json({
                message: "Internal Server Error",
                status: false,
                error: error.message
            });
        }
    }

    public async getLearnerPlan(req: CustomRequest, res: Response): Promise<Response> {
        try {
            const learnerPlanRepository = AppDataSource.getRepository(LearnerPlan);
            const { id } = req.params;

            const learnerPlan = await learnerPlanRepository.createQueryBuilder('learnerPlan')
                .leftJoinAndSelect('learnerPlan.assessor_id', 'assessor')
                .leftJoinAndSelect('learnerPlan.learners', 'learner')
                .leftJoinAndSelect('learnerPlan.courses', 'course')
                .where('learnerPlan.learner_plan_id = :id', { id })
                .select([
                    'learnerPlan.learner_plan_id',
                    'learnerPlan.title',
                    'learnerPlan.description',
                    'learnerPlan.location',
                    'learnerPlan.startDate',
                    'learnerPlan.Duration',
                    'learnerPlan.type',
                    'learnerPlan.Attended',
                    'learnerPlan.repeatSession',
                    'assessor.user_id',
                    'assessor.user_name',
                    'assessor.email',
                    'learner.learner_id',
                    'learner.user_name',
                    'learner.email',
                    'course.course_id',
                    'course.course_name',
                    'course.course_code'
                ])
                .getOne();

            if (!learnerPlan) {
                return res.status(404).json({
                    message: "Learner plan not found",
                    status: false
                });
            }

            // Get filtered course list for this learner plan
            const learnerIds = learnerPlan.learners.map(l => l.learner_id);
            const filteredCourses = await this.getFilteredCourses(learnerPlan.assessor_id.user_id, learnerIds);

            return res.status(200).json({
                message: "Learner plan fetched successfully",
                status: true,
                data: {
                    ...learnerPlan,
                    filtered_course_list: filteredCourses
                }
            });

        } catch (error) {
            return res.status(500).json({
                message: "Internal Server Error",
                status: false,
                error: error.message
            });
        }
    }

    public async getLearnerPlansByMonth(req: CustomRequest, res: Response): Promise<Response> {
        try {
            const learnerPlanRepository = AppDataSource.getRepository(LearnerPlan);
            const { year, month, learner_id, assessor_id } = req.query;

            if (!year || !month) {
                return res.status(400).json({
                    message: "Year and month are required",
                    status: false
                });
            }

            const startDate = new Date(Number(year), Number(month) - 1, 1);
            const endDate = new Date(Number(year), Number(month), 0);

            const qb = learnerPlanRepository.createQueryBuilder('learnerPlan')
                .leftJoinAndSelect('learnerPlan.assessor_id', 'assessor')
                .leftJoinAndSelect('learnerPlan.learners', 'learner')
                .leftJoinAndSelect('learnerPlan.courses', 'course')
                .where('learnerPlan.startDate BETWEEN :startDate AND :endDate', { startDate, endDate });

            if (assessor_id) {
                qb.andWhere('assessor.user_id = :assessor_id', { assessor_id });
            }

            if (learner_id) {
                qb.andWhere('learner.learner_id = :learner_id', { learner_id });
            }

            const learnerPlans = await qb
                .orderBy('learnerPlan.startDate', 'ASC')
                .getMany();

            return res.status(200).json({
                message: "Learner plans fetched successfully",
                status: true,
                data: learnerPlans
            });

        } catch (error) {
            return res.status(500).json({
                message: "Internal Server Error",
                status: false,
                error: error.message
            });
        }
    }

    public async testLearnerPlanNotification(req: CustomRequest, res: Response): Promise<Response> {
        try {
            const { learner_id } = req.body;

            if (!learner_id) {
                return res.status(400).json({
                    message: "Learner ID is required",
                    status: false
                });
            }

            // Test learner plan data with future date
            const futureDate = new Date();
            futureDate.setDate(futureDate.getDate() + 7); // 7 days from now

            const testNotificationData = {
                data: {
                    title: "Test Learner Plan Notification",
                    message: `You have a test learner plan scheduled for ${futureDate.toISOString().split('T')[0]}`,
                    type: NotificationType.Notification
                },
                domain: SocketDomain.CourseAllocation
            };

            console.log('📧 Sending test learner plan notification...');

            try {
                await SendNotification(learner_id, testNotificationData);
                console.log(`✅ Test notification sent successfully to learner ${learner_id}`);

                return res.status(200).json({
                    message: "Test learner plan notification sent successfully",
                    status: true,
                    data: {
                        learner_id,
                        notification_data: testNotificationData
                    }
                });
            } catch (error) {
                console.error(`❌ Failed to send test notification to learner ${learner_id}:`, error);
                return res.status(500).json({
                    message: "Failed to send test notification",
                    status: false,
                    error: error.message
                });
            }

        } catch (error) {
            console.error('❌ Error in testLearnerPlanNotification:', error);
            return res.status(500).json({
                message: "Internal Server Error",
                status: false,
                error: error.message
            });
        }
    }

}

export default LearnerPlanController;

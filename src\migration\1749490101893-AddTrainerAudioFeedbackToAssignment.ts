import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTrainerAudioFeedbackToAssignment1749490101893 implements MigrationInterface {
    name = 'AddTrainerAudioFeedbackToAssignment1749490101893'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add trainer_audio_feedback column to assignment table
        await queryRunner.query(`ALTER TABLE "assignment" ADD "trainer_audio_feedback" json`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove trainer_audio_feedback column from assignment table
        await queryRunner.query(`ALTER TABLE "assignment" DROP COLUMN "trainer_audio_feedback"`);
    }

}

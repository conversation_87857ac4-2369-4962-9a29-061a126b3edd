import { SendEmailTemplet } from "./nodemailer";

export const sendPasswordByEmail = async (email: string, password: any): Promise<boolean> => {
    try {
        const html = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    font-family: 'Arial', sans-serif;
                    background-color: #f0f0f0;
                    margin: 0;
                    padding: 0;
                    text-align: center;
                }
        
                .container {
                    max-width: 600px;
                    margin: 20px auto;
                    background-color: #ffffff;
                    padding: 20px;
                    border-radius: 10px;
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                }
        
                .logo {
                    max-width: 150px;
                    height: auto;
                    margin-bottom: 20px;
                }
        
                .title {
                    font-size: 24px;
                    font-weight: bold;
                    margin-bottom: 10px;
                    color: #333;
                }
        
                .message {
                    font-size: 16px;
                    margin-bottom: 20px;
                    color: #555;
                }
        
                .password {
                    font-weight: bold;
                    font-size: 20px;
                    color: #3498db;
                }
        
                .footer {
                    font-size: 16px;
                    color: #777;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <img class="logo" src="https://jeel1.s3.ap-south-1.amazonaws.com/logo/logo.svg" alt="Locker Logo">
        
                <div class="title">Password reset</div>
                <div class="message">
                    <p>Congratulations! Your account has been successfully created.</p>
                </div>
                <div class="password">Your new password is: <strong>${password}</strong></div>
                <div class="footer">
                    <p>Thank you for using Locker.</p>
                </div>
            </div>
        </body>
        </html>
        `

        const responce = await SendEmailTemplet(email, "Welcome", null, html)
        return true
    } catch (error) {
        console.log(error)
        return true
    }
}

export const resetPasswordByEmail = async (email: string, resetLink: string): Promise<boolean> => {
    try {
        const html = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    font-family: 'Arial', sans-serif;
                    background-color: #f4f4f4;
                    margin: 0;
                    padding: 0;
                    text-align: center;
                }

                .container {
                    max-width: 600px;
                    margin: 30px auto;
                    background-color: #ffffff;
                    padding: 30px;
                    border-radius: 8px;
                    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
                }

                .logo {
                    max-width: 120px;
                    height: auto;
                    margin-bottom: 25px;
                }

                .title {
                    font-size: 26px;
                    font-weight: bold;
                    margin-bottom: 20px;
                    color: #333;
                }

                .message {
                    font-size: 16px;
                    line-height: 1.6;
                    color: #555;
                    margin-bottom: 30px;
                }

                .reset-btn {
                    display: inline-block;
                    padding: 15px 25px;
                    font-size: 16px;
                    color: #ffffff;
                    background-color: #3498db;
                    text-decoration: none;
                    border-radius: 5px;
                    box-shadow: 0 3px 6px rgba(52, 152, 219, 0.4);
                    transition: background-color 0.3s;
                }

                .reset-btn:hover {
                    background-color: #2980b9;
                }

                .footer {
                    font-size: 14px;
                    color: #888;
                    margin-top: 30px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <img class="logo" src="https://jeel1.s3.ap-south-1.amazonaws.com/logo/logo.svg" alt="Locker Logo">

                <div class="title">Reset Your Password</div>
                <div class="message">
                    <p>We received a request to reset your password. Click the button below to reset your password. This link will expire in 24 hours.</p>
                </div>
                <a href="${resetLink}" class="reset-btn">Reset Password</a>
                <div class="footer">
                    <p>If you did not request a password reset, you can ignore this email. Your password will remain the same.</p>
                    <p>Thank you for using Locker.</p>
                </div>
            </div>
        </body>
        </html>
        `;

        const response = await SendEmailTemplet(email, "Password Reset Request", null, html);
        return true;
    } catch (error) {
        console.log(error);
        return false;
    }
};


const generateOTP = (): string => {
    const digits = '0123456789';
    let OTP = '';
    for (let i = 0; i < 6; i++) {
        OTP += digits[Math.floor(Math.random() * 10)];
    }
    return OTP;
};

export const sendOtpByEmail = async (email: string): Promise<any> => {
    const otp = generateOTP();

    const html = `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {
                font-family: 'Arial', sans-serif;
                background-color: #f0f0f0;
                margin: 0;
                padding: 0;
                text-align: center;
            }
    
            .container {
                max-width: 600px;
                margin: 20px auto;
                background-color: #ffffff;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }
    
            .logo {
                max-width: 150px;
                height: auto;
                margin-bottom: 20px;
            }
    
            .title {
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 20px;
                color: #333;
            }
    
            .message {
                font-size: 16px;
                margin-bottom: 20px;
                color: #555;
            }
    
            .otp {
                font-weight: bold;
                font-size: 32px;
                color: #3498db;
            }
    
            .footer {
                font-size: 16px;
                color: #777;
            }
        </style>
    </head>
    <body>
        <div class="container">
        <img class="adapt-img" src="https://jeel1.s3.ap-south-1.amazonaws.com/logo/logo.svg" alt style="display: block;" width="180">
            <div class="title">One-Time Password</div>
            <div class="message">
                <p>You are receiving this email because a request has been made to reset the password for your Locker account.</p>
            </div>
            <div class="otp"><strong>${otp}</strong></div>
            <div class="footer">
                <p>If you did not request a password reset or have any concerns, please ignore this email.</p>
                <p>Thank you for using Locker.</p>
            </div>
        </div>
    </body>
    </html> `;

    const response = await SendEmailTemplet(email, "Locker - One-Time Password for Your Account", null, html);

    return otp;
};

export const sendUserEmail = async (email: string, data: any): Promise<any> => {

    const html = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    font-family: 'Arial', sans-serif;
                    background-color: #f0f0f0;
                    margin: 0;
                    padding: 0;
                    text-align: center;
                }

                .container {
                    max-width: 600px;
                    margin: 20px auto;
                    background-color: #ffffff;
                    padding: 20px;
                    border-radius: 10px;
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                }

                .text-section {
                    font-size: 14px;
                    color: #333;
                    text-align: left;
                }

                a {
                    color: #3498db;
                    text-decoration: none;
                }

                a:hover {
                    text-decoration: underline;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="text-section">
                    <p>${data.message}</p>
                    <p>Regards,</p>
                    <p>${data.adminName}</p>
                    <hr />
                    <p>DO NOT REPLY TO THIS EMAIL! To reply please login into the system: 
                    <a href="${process.env.FRONTEND}">Locker</a></p>
                    <p>Sent from the Locker system.</p>
                </div>
            </div>
        </body>
        </html>
        `;

    const response = await SendEmailTemplet(email, data.subject, null, html);

    return true;
};

export const sendSessionInviteEmail = async (
    learnerEmail: string,
    sessionData: {
        title: string;
        description: string;
        trainerName: string;
        startDate: string;
        endDate: string;
        location: string;
        duration: number;
    }
): Promise<boolean> => {
    try {
        // Format dates for display
        const startDateTime = new Date(sessionData.startDate);
        const endDateTime = new Date(sessionData.endDate);

        const formatDate = (date: Date) => {
            return date.toLocaleDateString('en-GB', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        };

        const formatTime = (date: Date) => {
            return date.toLocaleTimeString('en-GB', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
        };

        // Create calendar event data
        const calendarEvent = generateCalendarEvent(sessionData);

        const html = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    font-family: 'Arial', sans-serif;
                    background-color: #f4f4f4;
                    margin: 0;
                    padding: 0;
                    text-align: center;
                }

                .container {
                    max-width: 600px;
                    margin: 30px auto;
                    background-color: #ffffff;
                    padding: 30px;
                    border-radius: 8px;
                    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
                }

                .logo {
                    max-width: 120px;
                    height: auto;
                    margin-bottom: 25px;
                }

                .title {
                    font-size: 26px;
                    font-weight: bold;
                    margin-bottom: 20px;
                    color: #2c3e50;
                }

                .session-card {
                    background-color: #f8f9fa;
                    border-left: 4px solid #3498db;
                    padding: 20px;
                    margin: 20px 0;
                    text-align: left;
                    border-radius: 5px;
                }

                .session-title {
                    font-size: 20px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 10px;
                }

                .session-details {
                    font-size: 16px;
                    line-height: 1.6;
                    color: #555;
                }

                .detail-row {
                    margin-bottom: 8px;
                }

                .detail-label {
                    font-weight: bold;
                    color: #2c3e50;
                    display: inline-block;
                    width: 100px;
                }

                .calendar-button {
                    display: inline-block;
                    background-color: #3498db;
                    color: white;
                    padding: 12px 24px;
                    text-decoration: none;
                    border-radius: 5px;
                    font-weight: bold;
                    margin: 20px 0;
                }

                .calendar-button:hover {
                    background-color: #2980b9;
                }

                .footer {
                    font-size: 14px;
                    color: #777;
                    margin-top: 30px;
                    border-top: 1px solid #eee;
                    padding-top: 20px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <img class="logo" src="https://jeel1.s3.ap-south-1.amazonaws.com/logo/logo.svg" alt="Locker Logo">

                <div class="title">Training Session Invitation</div>

                <div class="session-card">
                    <div class="session-title">${sessionData.title}</div>
                    <div class="session-details">
                        <div class="detail-row">
                            <span class="detail-label">Trainer:</span> ${sessionData.trainerName}
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Date:</span> ${formatDate(startDateTime)}
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Time:</span> ${formatTime(startDateTime)} - ${formatTime(endDateTime)}
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Duration:</span> ${sessionData.duration} minutes
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Location:</span> ${sessionData.location || 'To be confirmed'}
                        </div>
                        ${sessionData.description ? `
                        <div class="detail-row" style="margin-top: 15px;">
                            <span class="detail-label">Description:</span><br>
                            ${sessionData.description}
                        </div>
                        ` : ''}
                    </div>
                </div>

                <a href="data:text/calendar;charset=utf8,${encodeURIComponent(calendarEvent)}"
                   class="calendar-button"
                   download="training-session.ics">
                    📅 Add to Calendar
                </a>

                <div class="footer">
                    <p>Please make sure to attend this training session on time.</p>
                    <p>If you have any questions, please contact your trainer.</p>
                    <p>Thank you for using Locker.</p>
                </div>
            </div>
        </body>
        </html>`;

        const response = await SendEmailTemplet(
            learnerEmail,
            `Training Session Invitation: ${sessionData.title}`,
            null,
            html
        );

        return true;
    } catch (error) {
        console.log('Error sending session invite email:', error);
        return false;
    }
};

// Helper function to generate calendar event in ICS format
const generateCalendarEvent = (sessionData: {
    title: string;
    description: string;
    trainerName: string;
    startDate: string;
    endDate: string;
    location: string;
}) => {
    const startDate = new Date(sessionData.startDate);
    const endDate = new Date(sessionData.endDate);

    // Format dates for ICS (YYYYMMDDTHHMMSSZ)
    const formatICSDate = (date: Date) => {
        return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
    };

    const icsContent = `BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Locker//Training Session//EN
BEGIN:VEVENT
UID:${Date.now()}@locker.com
DTSTAMP:${formatICSDate(new Date())}
DTSTART:${formatICSDate(startDate)}
DTEND:${formatICSDate(endDate)}
SUMMARY:${sessionData.title}
DESCRIPTION:Training session with ${sessionData.trainerName}\\n\\n${sessionData.description || 'No additional description provided.'}
LOCATION:${sessionData.location || 'To be confirmed'}
ORGANIZER:CN=${sessionData.trainerName}
STATUS:CONFIRMED
TRANSP:OPAQUE
END:VEVENT
END:VCALENDAR`;

    return icsContent;
};
import { MigrationInterface, QueryRunner } from "typeorm";

export class AddAssignedGatewayColumns1718800000000 implements MigrationInterface {
    name = 'AddAssignedGatewayColumns1718800000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check and add assigned_gateway_id column
        const hasAssignedGatewayId = await queryRunner.hasColumn("course", "assigned_gateway_id");
        if (!hasAssignedGatewayId) {
            await queryRunner.query(`ALTER TABLE "course" ADD "assigned_gateway_id" integer NULL`);
        }

        // Check and add assigned_gateway_name column
        const hasAssignedGatewayName = await queryRunner.hasColumn("course", "assigned_gateway_name");
        if (!hasAssignedGatewayName) {
            await queryRunner.query(`ALTER TABLE "course" ADD "assigned_gateway_name" varchar NULL`);
        }

        // Check and add checklist column
        const hasChecklist = await queryRunner.hasColumn("course", "checklist");
        if (!hasChecklist) {
            await queryRunner.query(`ALTER TABLE "course" ADD "checklist" json NULL DEFAULT '[]'`);
        }

        // Check and add assigned_standards column
        const hasAssignedStandards = await queryRunner.hasColumn("course", "assigned_standards");
        if (!hasAssignedStandards) {
            await queryRunner.query(`ALTER TABLE "course" ADD "assigned_standards" json NULL DEFAULT '[]'`);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the columns in reverse order
        await queryRunner.query(`ALTER TABLE "course" DROP COLUMN IF EXISTS "assigned_standards"`);
        await queryRunner.query(`ALTER TABLE "course" DROP COLUMN IF EXISTS "checklist"`);
        await queryRunner.query(`ALTER TABLE "course" DROP COLUMN "assigned_gateway_name"`);
        await queryRunner.query(`ALTER TABLE "course" DROP COLUMN "assigned_gateway_id"`);
    }
}

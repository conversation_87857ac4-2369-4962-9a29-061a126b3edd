{"name": "E-Portfolio-Backend", "version": "0.0.1", "description": "Awesome project developed with TypeORM.", "type": "commonjs", "devDependencies": {"@types/dotenv": "^8.2.0", "@types/express": "^4.17.21", "@types/node": "^16.18.66", "@types/pdfkit": "^0.13.9", "nodemon": "^3.0.2", "ts-node": "10.7.0", "typescript": "^5.3.2"}, "dependencies": {"@aws-sdk/client-s3": "^3.456.0", "@aws-sdk/s3-request-presigner": "^3.456.0", "@supabase/supabase-js": "^2.38.5", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/mqtt": "^2.5.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "child_process": "^1.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mime-types": "^2.1.35", "morgan": "^1.10.0", "mqtt": "^5.3.5", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "nodemailer": "^6.9.7", "nodemailer-smtp-transport": "^2.7.4", "pdfkit": "^0.17.0", "pg": "^8.4.0", "reflect-metadata": "^0.1.13", "typeorm": "^0.3.20", "ws": "^8.18.0", "xlsx": "^0.18.5"}, "scripts": {"start": "ts-node main.ts", "build": "tsc", "dev": "nodemon main.ts", "typeorm": "typeorm-ts-node-commonjs", "start:dev": "npm run set-env:dev && nodemon --ignore temp.json --ignore temp.pdf main.ts", "start:local": "npm run set-env:local && nodemon --ignore temp.json --ignore temp.pdf main.ts", "set-env:dev": "copy .env.dev .env", "set-env:local": "copy .env.local .env", "migration:run": "npm run typeorm migration:run -- -d src/data-source.ts", "migration:generate": "npm run typeorm migration:generate -- -d src/data-source.ts"}, "nodemonConfig": {"ignore": ["temp.json", "temp.pdf"]}}
import { MigrationInterface, QueryRunner } from "typeorm";

export class AddEvidenceTimeLogToAssignment1749491657118 implements MigrationInterface {
    name = 'AddEvidenceTimeLogToAssignment1749491657118'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add evidence_time_log column to assignment table
        await queryRunner.query(`ALTER TABLE "assignment" ADD "evidence_time_log" boolean DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove evidence_time_log column from assignment table
        await queryRunner.query(`ALTER TABLE "assignment" DROP COLUMN "evidence_time_log"`);
    }

}

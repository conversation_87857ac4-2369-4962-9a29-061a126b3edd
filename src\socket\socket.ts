import WebSocket from 'ws';
import url from 'url';

interface Client extends WebSocket {
    _userId?: string;
}

const userClientMap: Map<string, WebSocket[]> = new Map();

function getClientIdFromUrl(requestUrl: string): string | null {
    try {
        const parsedUrl = url.parse(requestUrl, true);
        return parsedUrl.query && parsedUrl.query.id ? String(parsedUrl.query.id) : null;
    } catch (error) {
        console.error('Error parsing URL:', error);
        return null;
    }
}


function removeClientFromMap(clientId: string, client: WebSocket): void {
    const clients = userClientMap.get(clientId);
    if (clients) {
        const updatedClients = clients.filter(c => c !== client);
        if (updatedClients.length > 0) {
            userClientMap.set(clientId, updatedClients);
        } else {
            userClientMap.delete(clientId);
        }
    }
}

function connection(client: Client, req: any): void {
    console.log("🔌 Client Connected");

    const clientId = getClientIdFromUrl(req.url);
    if (!clientId) {
        console.log("❌ No client ID provided, closing connection");
        client.close(1008, 'Client ID required');
        return;
    }

    // Store client connection
    if (userClientMap.has(clientId)) {
        const existingClients = userClientMap.get(clientId) || [];
        userClientMap.set(clientId, [...existingClients, client]);
    } else {
        userClientMap.set(clientId, [client]);
    }

    console.log(`✅ User ${clientId} connected. Total connections: ${userClientMap.get(clientId)?.length}`);
    console.log(`📊 Total users connected: ${userClientMap.size}`);

    // Send welcome message
    client.send(JSON.stringify({
        data: { message: "Connected successfully" },
        domain: "connection",
        timestamp: new Date().toISOString()
    }));

    client.on('close', () => {
        removeClientFromMap(clientId, client);
        console.log(`🔌 User ${clientId} disconnected`);
    });

    client.on('error', (error) => {
        console.error(`❌ WebSocket error for user ${clientId}:`, error);
        removeClientFromMap(clientId, client);
    });
}

export function initSocket(server: any): void {
    const wss = new WebSocket.Server({ server });

    wss.on('connection', connection);

    console.log("🚀 WebSocket server initialized");

    // Heartbeat to keep connections alive
    setInterval(() => {
        console.log(`💓 Heartbeat - Active connections: ${wss.clients.size}`);
        wss.clients.forEach(client => {
            if (client.readyState === WebSocket.OPEN) {
                client.send(JSON.stringify({
                    data: { type: "heartbeat" },
                    domain: "system",
                    timestamp: new Date().toISOString()
                }));
            }
        });
    }, 25000);
}

export function sendDataToUser(userIds: number[], data: any): void {
    console.log(`📤 Sending notification to users: ${userIds}`);

    let successCount = 0;
    let failureCount = 0;

    userIds.forEach(userId => {
        if (userId) {
            const userIdString = userId.toString();
            const clients = userClientMap.get(userIdString);

            if (clients && clients.length > 0) {
                clients.forEach(client => {
                    if (client.readyState === WebSocket.OPEN) {
                        try {
                            client.send(JSON.stringify({
                                ...data,
                                timestamp: new Date().toISOString()
                            }));
                            successCount++;
                        } catch (error) {
                            console.error(`❌ Error sending to user ${userId}:`, error);
                            failureCount++;
                        }
                    } else {
                        console.log(`⚠️ Client for user ${userId} is not open`);
                        failureCount++;
                    }
                });
            } else {
                console.log(`⚠️ No active connections for user ${userId}`);
                failureCount++;
            }
        }
    });

    console.log(`📊 Notification sent - Success: ${successCount}, Failed: ${failureCount}`);
}

// Debug function to check active connections
export function getActiveConnections(): { [userId: string]: number } {
    const connections: { [userId: string]: number } = {};
    userClientMap.forEach((clients, userId) => {
        connections[userId] = clients.filter(client => client.readyState === WebSocket.OPEN).length;
    });
    return connections;
}

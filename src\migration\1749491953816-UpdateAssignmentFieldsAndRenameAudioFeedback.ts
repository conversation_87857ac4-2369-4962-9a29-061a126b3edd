import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateAssignmentFieldsAndRenameAudioFeedback1749491953816 implements MigrationInterface {
    name = 'UpdateAssignmentFieldsAndRenameAudioFeedback1749491953816'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Step 1: Add evidence_time_log column
        await queryRunner.query(`ALTER TABLE "assignment" ADD "evidence_time_log" boolean DEFAULT false`);

        // Step 2: Check if external_feedback column exists before renaming
        const hasExternalFeedback = await queryRunner.hasColumn("assignment", "external_feedback");

        if (hasExternalFeedback) {
            // Rename external_feedback to trainer_audio_feedback
            await queryRunner.query(`ALTER TABLE "assignment" RENAME COLUMN "external_feedback" TO "trainer_audio_feedback"`);
        } else {
            // If external_feedback doesn't exist, create trainer_audio_feedback column
            await queryRunner.query(`ALTER TABLE "assignment" ADD "trainer_audio_feedback" json`);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Step 1: Remove evidence_time_log column
        await queryRunner.query(`ALTER TABLE "assignment" DROP COLUMN IF EXISTS "evidence_time_log"`);

        // Step 2: Check if trainer_audio_feedback column exists before renaming back
        const hasTrainerAudioFeedback = await queryRunner.hasColumn("assignment", "trainer_audio_feedback");

        if (hasTrainerAudioFeedback) {
            // Rename trainer_audio_feedback back to external_feedback
            await queryRunner.query(`ALTER TABLE "assignment" RENAME COLUMN "trainer_audio_feedback" TO "external_feedback"`);
        }
    }

}

<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test for User 19</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .message { background-color: #f8f9fa; padding: 10px; margin: 5px 0; border-left: 3px solid #007bff; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>WebSocket Test for User 19</h1>
    
    <div id="status" class="status disconnected">Disconnected</div>
    
    <button onclick="connect()">Connect</button>
    <button onclick="disconnect()">Disconnect</button>
    <button onclick="clearMessages()">Clear Messages</button>
    
    <h3>Messages:</h3>
    <div id="messages"></div>
    
    <script>
        let socket = null;
        const userId = 19;
        
        function connect() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                console.log('Already connected');
                return;
            }
            
            const wsUrl = `ws://localhost:4000?id=${userId}`;
            socket = new WebSocket(wsUrl);
            
            socket.onopen = function() {
                updateStatus('Connected', true);
                addMessage('✅ Connected to WebSocket server', 'success');
            };
            
            socket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                addMessage(`📨 Received: ${JSON.stringify(data, null, 2)}`, 'info');
            };
            
            socket.onclose = function() {
                updateStatus('Disconnected', false);
                addMessage('🔌 WebSocket connection closed', 'warning');
            };
            
            socket.onerror = function(error) {
                updateStatus('Error', false);
                addMessage(`❌ WebSocket error: ${error}`, 'error');
            };
        }
        
        function disconnect() {
            if (socket) {
                socket.close();
            }
        }
        
        function updateStatus(text, isConnected) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = text;
            statusDiv.className = `status ${isConnected ? 'connected' : 'disconnected'}`;
        }
        
        function addMessage(message, type) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.innerHTML = `<small>${new Date().toLocaleTimeString()}</small><br>${message}`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }
        
        // Auto-connect on page load
        connect();
    </script>
</body>
</html>
